/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.0+4110"}
 */

/**
 * Import the modules used in this configuration.
 */
const ADC12   = scripting.addModule("/ti/driverlib/ADC12", {}, false);
const ADC121  = ADC12.addInstance();
const GPIO    = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1   = GPIO.addInstance();
const I2C     = scripting.addModule("/ti/driverlib/I2C", {}, false);
const I2C1    = I2C.addInstance();
const SYSCTL  = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK = scripting.addModule("/ti/driverlib/SYSTICK");
const TIMER   = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1  = TIMER.addInstance();
const UART    = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1   = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
const divider9       = system.clockTree["UDIV"];
divider9.divideValue = 2;

const gate7  = system.clockTree["MFCLKGATE"];
gate7.enable = true;

const gate8  = system.clockTree["MFPCLKGATE"];
gate8.enable = true;

const multiplier2         = system.clockTree["PLL_QDIV"];
multiplier2.multiplyValue = 4;

const mux4       = system.clockTree["EXHFMUX"];
mux4.inputSelect = "EXHFMUX_XTAL";

const mux8       = system.clockTree["HSCLKMUX"];
mux8.inputSelect = "HSCLKMUX_SYSPLL0";

const mux12       = system.clockTree["SYSPLLMUX"];
mux12.inputSelect = "zSYSPLLMUX_HFCLK";

const pinFunction4     = system.clockTree["HFXT"];
pinFunction4.inputFreq = 40;
pinFunction4.enable    = true;

ADC121.$name                             = "ADC1";
ADC121.sampClkDiv                        = "DL_ADC12_CLOCK_DIVIDE_8";
ADC121.repeatMode                        = true;
ADC121.adcMem0_name                      = "ADC_Channel0";
ADC121.powerDownMode                     = "DL_ADC12_POWER_DOWN_MODE_MANUAL";
ADC121.adcPin0Config.$name               = "ti_driverlib_gpio_GPIOPinGeneric0";
ADC121.adcPin0Config.hideOutputInversion = scripting.forceWrite(false);

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO1.$name                         = "Gray_Address";
GPIO1.associatedPins.create(3);
GPIO1.associatedPins[0].$name       = "PIN_0";
GPIO1.associatedPins[0].pin.$assign = "PB0";
GPIO1.associatedPins[1].$name       = "PIN_1";
GPIO1.associatedPins[1].pin.$assign = "PB1";
GPIO1.associatedPins[2].$name       = "PIN_2";
GPIO1.associatedPins[2].pin.$assign = "PB2";

I2C1.$name                     = "I2C_0";
I2C1.basicEnableController     = true;
I2C1.advControllerRXFIFOTRIG   = "BYTES_8";
I2C1.advControllerTXFIFOTRIG   = "BYTES_7";
I2C1.peripheral.$assign        = "I2C0";
I2C1.peripheral.sdaPin.$assign = "PA0";
I2C1.peripheral.sclPin.$assign = "PA1";
I2C1.sdaPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric3";
I2C1.sclPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric4";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;
scripting.suppress("For best practices when the CPUCLK is running at 32MHz and above, clear the flash status bit using DL_FlashCTL_executeClearStatus\\(\\) before executing any flash operation\\. Otherwise there may be false positives\\.", SYSCTL);

SYSTICK.periodEnable  = true;
SYSTICK.period        = 40;
SYSTICK.systickEnable = true;

TIMER1.$name              = "TIMER_1";
TIMER1.timerMode          = "PERIODIC";
TIMER1.timerPeriod        = "1 ms";
TIMER1.timerStartTimer    = true;
TIMER1.interrupts         = ["ZERO"];
TIMER1.interruptPriority  = "0";
TIMER1.peripheral.$assign = "TIMG12";

UART1.$name             = "UART_0";
UART1.targetBaudRate    = 115200;
UART1.uartClkDiv        = "2";
UART1.txPinConfig.$name = "ti_driverlib_gpio_GPIOPinGeneric1";
UART1.rxPinConfig.$name = "ti_driverlib_gpio_GPIOPinGeneric2";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
pinFunction4.peripheral.$suggestSolution           = "SYSCTL";
pinFunction4.peripheral.hfxInPin.$suggestSolution  = "PA5";
pinFunction4.peripheral.hfxOutPin.$suggestSolution = "PA6";
ADC121.peripheral.$suggestSolution                 = "ADC0";
ADC121.peripheral.adcPin0.$suggestSolution         = "PA27";
Board.peripheral.$suggestSolution                  = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution         = "PA20";
Board.peripheral.swdioPin.$suggestSolution         = "PA19";
UART1.peripheral.$suggestSolution                  = "UART0";
UART1.peripheral.rxPin.$suggestSolution            = "PA11";
UART1.peripheral.txPin.$suggestSolution            = "PA10";
