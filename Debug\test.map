******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jul 31 00:40:12 2025

OUTPUT FILE NAME:   <test.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00001b29


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00001e60  0001e1a0  R  X
  SRAM                  20200000   00008000  00001340  00006cc0  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00001e60   00001e60    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00001c58   00001c58    r-x .text
  00001d18    00001d18    00000110   00000110    r-- .rodata
  00001e28    00001e28    00000038   00000038    r-- .cinit
20200000    20200000    00001140   00000000    rw-
  20200000    20200000    00001000   00000000    rw- .sysmem
  20201000    20201000    00000130   00000000    rw- .data
  20201130    20201130    00000010   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00001c58     
                  000000c0    00000320     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  000003e0    00000284     libc.a : _printfi.c.obj (.text:__TI_printfi_minimal)
                  00000664    00000234     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  00000898    000001b0     empty.o (.text.main)
                  00000a48    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00000bda    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00000bdc    00000170     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  00000d4c    00000120     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  00000e6c    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00000f78    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00001060    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00001144    000000e0     ADC.o (.text.adc_getValue)
                  00001224    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00001300    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  0000139a    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  0000139c    0000007c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00001418    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00001494    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00001508    00000068                            : comparedf2.c.obj (.text.__ledf2)
                  00001570    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  000015d4    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00001636    00000062     libc.a : memset16.S.obj (.text:memset)
                  00001698    00000060     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  000016f8    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_0_init)
                  0000174c    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000017a0    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  000017ec    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00001836    00000002     --HOLE-- [fill = 0]
                  00001838    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00001880    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000018c8    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_1_init)
                  00001910    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00001954    00000044     Time.o (.text.delay_ms)
                  00001998    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  000019d8    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00001a18    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00001a54    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  00001a8e    00000002     --HOLE-- [fill = 0]
                  00001a90    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  00001ac8    00000034     Uart.o (.text.uart0_send_string)
                  00001afc    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00001b28    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00001b50    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00001b76    00000024     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00001b9a    00000002     --HOLE-- [fill = 0]
                  00001b9c    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00001bc0    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00001be2    00000020     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Normalize_For_User)
                  00001c02    0000001e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00001c20    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00001c3c    0000001c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00001c58    00000018     libc.a : sprintf.c.obj (.text._outs)
                  00001c70    00000016            : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00001c86    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00001c98    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  00001caa    00000002     --HOLE-- [fill = 0]
                  00001cac    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00001cba    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00001cc8    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00001cd4    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00001cde    0000000a     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00001ce8    0000000a     libc.a : sprintf.c.obj (.text._outc)
                  00001cf2    00000002     --HOLE-- [fill = 0]
                  00001cf4    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00001cfc    00000006     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  00001d02    00000006     libc.a : exit.c.obj (.text:abort)
                  00001d08    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00001d0c    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00001d10    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00001d14    00000004     --HOLE-- [fill = 0]

.cinit     0    00001e28    00000038     
                  00001e28    00000012     (.cinit..data.load) [load image, compression = lzss]
                  00001e3a    00000002     --HOLE-- [fill = 0]
                  00001e3c    0000000c     (__TI_handler_table)
                  00001e48    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00001e50    00000010     (__TI_cinit_table)

.rodata    0    00001d18    00000110     
                  00001d18    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00001d40    00000024     empty.o (.rodata.str1.11898133897667081452.1)
                  00001d64    00000021     empty.o (.rodata.str1.17669528882079347314.1)
                  00001d85    00000021     empty.o (.rodata.str1.9517790425240694019.1)
                  00001da6    00000002     ti_msp_dl_config.o (.rodata.gI2C_0ClockConfig)
                  00001da8    00000014     ti_msp_dl_config.o (.rodata.gTIMER_1TimerConfig)
                  00001dbc    00000011     libc.a : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00001dcd    0000000e     empty.o (.rodata.str1.254342170260855183.1)
                  00001ddb    0000000d     empty.o (.rodata.str1.14685083708502177989.1)
                  00001de8    0000000d     No_Mcu_Ganv_Grayscale_Sensor.o (.rodata.str1.841257358316167677.1)
                  00001df5    00000001     --HOLE-- [fill = 0]
                  00001df6    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00001e00    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00001e08    00000008     No_Mcu_Ganv_Grayscale_Sensor.o (.rodata.str1.11984164251132177970.1)
                  00001e10    00000008     No_Mcu_Ganv_Grayscale_Sensor.o (.rodata.str1.1610872482944133404.1)
                  00001e18    00000007     No_Mcu_Ganv_Grayscale_Sensor.o (.rodata.str1.16765855479592937698.1)
                  00001e1f    00000003     ti_msp_dl_config.o (.rodata.gTIMER_1ClockConfig)
                  00001e22    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00001e24    00000004     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.sysmem    0    20200000    00001000     UNINITIALIZED
                  20200000    00000010     libc.a : memory.c.obj (.sysmem)
                  20200010    00000ff0     --HOLE--

.data      0    20201000    00000130     UNINITIALIZED
                  20201000    00000100     empty.o (.data.rx_buff)
                  20201100    00000010     empty.o (.data.Anolog)
                  20201110    00000010     empty.o (.data.black)
                  20201120    00000010     empty.o (.data.white)

.bss       0    20201130    00000010     UNINITIALIZED
                  20201130    00000010     (.common:Normal)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code   ro data   rw data
       ------                           ----   -------   -------
    .\
       empty.o                          432    129       320    
       ti_msp_dl_config.o               672    85        0      
       startup_mspm0g350x_ticlang.o     6      192       0      
    +--+--------------------------------+------+---------+---------+
       Total:                           1110   406       320    
                                                                
    .\BSP\
       ADC.o                            224    0         0      
       Time.o                           68     0         0      
       Uart.o                           52     0         0      
    +--+--------------------------------+------+---------+---------+
       Total:                           344    0         0      
                                                                
    .\User\
       No_Mcu_Ganv_Grayscale_Sensor.o   2098   36        0      
    +--+--------------------------------+------+---------+---------+
       Total:                           2098   36        0      
                                                                
    D:/TI/CCS/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o     388    0         0      
       dl_timer.o                       260    0         0      
       dl_uart.o                        90     0         0      
       dl_adc12.o                       64     0         0      
       dl_i2c.o                         38     0         0      
       dl_common.o                      10     0         0      
    +--+--------------------------------+------+---------+---------+
       Total:                           850    0         0      
                                                                
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                   658    17        0      
       memcpy16.S.obj                   154    0         0      
       copy_decompress_lzss.c.obj       124    0         0      
       memset16.S.obj                   98     0         0      
       sprintf.c.obj                    90     0         0      
       autoinit.c.obj                   60     0         0      
       boot_cortex_m.c.obj              40     0         0      
       memccpy.c.obj                    34     0         0      
       copy_zero_init.c.obj             22     0         0      
       copy_decompress_none.c.obj       18     0         0      
       exit.c.obj                       6      0         0      
       pre_init.c.obj                   4      0         0      
    +--+--------------------------------+------+---------+---------+
       Total:                           1308   17        0      
                                                                
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4      0         0      
    +--+--------------------------------+------+---------+---------+
       Total:                           4      0         0      
                                                                
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     402    0         0      
       divdf3.S.obj                     268    0         0      
       muldf3.S.obj                     228    0         0      
       comparedf2.c.obj                 220    0         0      
       aeabi_dcmp.S.obj                 98     0         0      
       fixdfsi.S.obj                    74     0         0      
       aeabi_uidivmod.S.obj             64     0         0      
       muldsi3.S.obj                    58     0         0      
       floatsidf.S.obj                  44     0         0      
       floatunsidf.S.obj                36     0         0      
       aeabi_memset.S.obj               26     0         0      
       aeabi_memcpy.S.obj               8      0         0      
       aeabi_div0.c.obj                 2      0         0      
    +--+--------------------------------+------+---------+---------+
       Total:                           1528   0         0      
                                                                
       Heap:                            0      0         4096   
       Stack:                           0      0         512    
       Linker Generated:                0      54        0      
    +--+--------------------------------+------+---------+---------+
       Grand Total:                     7242   513       4928   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00001e50 records: 2, size/record: 8, table size: 16
	.data: load addr=00001e28, load size=00000012 bytes, run addr=20201000, run size=00000130 bytes, compression=lzss
	.bss: load addr=00001e48, load size=00000008 bytes, run addr=20201130, run size=00000010 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00001e3c records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00000bdb  ADC0_IRQHandler                      
00000bdb  ADC1_IRQHandler                      
00000bdb  AES_IRQHandler                       
20201100  Anolog                               
00001d08  C$$EXIT                              
00000bdb  CANFD0_IRQHandler                    
00000bdb  DAC0_IRQHandler                      
00001999  DL_ADC12_setClockConfig              
00001cd5  DL_Common_delayCycles                
00001b51  DL_I2C_setClockConfig                
00001225  DL_SYSCTL_configSYSPLL               
00001571  DL_SYSCTL_setHFCLKSourceHFXTParams   
00001911  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00000f79  DL_Timer_initTimerMode               
00001c21  DL_Timer_setClockConfig              
00001839  DL_UART_init                         
00001c87  DL_UART_setClockConfig               
00000bdb  DMA_IRQHandler                       
00000bdb  Default_Handler                      
00000bdb  GROUP0_IRQHandler                    
00000bdb  GROUP1_IRQHandler                    
00000bdd  Get_Analog_value                     
00001c03  Get_Anolog_Value                     
00001cfd  Get_Digtal_For_User                  
00001be3  Get_Normalize_For_User               
00001d09  HOSTexit                             
00000bdb  HardFault_Handler                    
00000bdb  I2C0_IRQHandler                      
00000bdb  I2C1_IRQHandler                      
00000bdb  NMI_Handler                          
000000c1  No_MCU_Ganv_Sensor_Init              
00001cdf  No_MCU_Ganv_Sensor_Init_Frist        
00000d4d  No_Mcu_Ganv_Sensor_Task_Without_tick 
20201130  Normal                               
00000bdb  PendSV_Handler                       
00000bdb  RTC_IRQHandler                       
00001d0d  Reset_Handler                        
00000bdb  SPI0_IRQHandler                      
00000bdb  SPI1_IRQHandler                      
00000bdb  SVC_Handler                          
000017a1  SYSCFG_DL_ADC1_init                  
00001881  SYSCFG_DL_GPIO_init                  
000016f9  SYSCFG_DL_I2C_0_init                 
0000139d  SYSCFG_DL_SYSCTL_init                
00001c3d  SYSCFG_DL_SYSTICK_init               
000018c9  SYSCFG_DL_TIMER_1_init               
00001699  SYSCFG_DL_UART_0_init                
00001b77  SYSCFG_DL_init                       
0000174d  SYSCFG_DL_initPower                  
00000bdb  SysTick_Handler                      
00000bdb  TIMA0_IRQHandler                     
00000bdb  TIMA1_IRQHandler                     
00000bdb  TIMG0_IRQHandler                     
00000bdb  TIMG12_IRQHandler                    
00000bdb  TIMG6_IRQHandler                     
00000bdb  TIMG7_IRQHandler                     
00000bdb  TIMG8_IRQHandler                     
00000bdb  UART0_IRQHandler                     
00000bdb  UART1_IRQHandler                     
00000bdb  UART2_IRQHandler                     
00000bdb  UART3_IRQHandler                     
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00001000  __SYSMEM_SIZE                        
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00001e50  __TI_CINIT_Base                      
00001e60  __TI_CINIT_Limit                     
00001e60  __TI_CINIT_Warm                      
00001e3c  __TI_Handler_Table_Base              
00001e48  __TI_Handler_Table_Limit             
00001a19  __TI_auto_init_nobinit_nopinit       
00001419  __TI_decompress_lzss                 
00001c99  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
000003e1  __TI_printfi_minimal                 
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00001c71  __TI_zero_init_nomemset              
00000a53  __adddf3                             
000017ed  __aeabi_d2iz                         
00000a53  __aeabi_dadd                         
000015d5  __aeabi_dcmpeq                       
00001611  __aeabi_dcmpge                       
00001625  __aeabi_dcmpgt                       
000015fd  __aeabi_dcmple                       
000015e9  __aeabi_dcmplt                       
00000e6d  __aeabi_ddiv                         
00001061  __aeabi_dmul                         
00000a49  __aeabi_dsub                         
00001afd  __aeabi_i2d                          
0000139b  __aeabi_idiv0                        
00001cc9  __aeabi_memclr                       
00001cc9  __aeabi_memclr4                      
00001cc9  __aeabi_memclr8                      
00001cf5  __aeabi_memcpy                       
00001cf5  __aeabi_memcpy4                      
00001cf5  __aeabi_memcpy8                      
00001cad  __aeabi_memset                       
00001cad  __aeabi_memset4                      
00001cad  __aeabi_memset8                      
00001b9d  __aeabi_ui2d                         
000019d9  __aeabi_uidiv                        
000019d9  __aeabi_uidivmod                     
ffffffff  __binit__                            
00001509  __cmpdf2                             
00000e6d  __divdf3                             
00001509  __eqdf2                              
000017ed  __fixdfsi                            
00001afd  __floatsidf                          
00001b9d  __floatunsidf                        
00001495  __gedf2                              
00001495  __gtdf2                              
00001509  __ledf2                              
00001509  __ltdf2                              
UNDEFED   __mpu_init                           
00001061  __muldf3                             
00001a55  __muldsi3                            
00001509  __nedf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00000a49  __subdf3                             
00001b29  _c_int00_noargs                      
20200000  _sys_memory                          
UNDEFED   _system_post_cinit                   
00001d11  _system_pre_init                     
00001d03  abort                                
00001145  adc_getValue                         
ffffffff  binit                                
20201110  black                                
00001955  delay_ms                             
00000000  interruptVectors                     
00000899  main                                 
00001bc1  memccpy                              
00001301  memcpy                               
00001637  memset                               
00000665  normalizeAnalogValues                
20201000  rx_buff                              
00001a91  sprintf                              
00001ac9  uart0_send_string                    
20201120  white                                


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  No_MCU_Ganv_Sensor_Init              
00000200  __STACK_SIZE                         
000003e1  __TI_printfi_minimal                 
00000665  normalizeAnalogValues                
00000899  main                                 
00000a49  __aeabi_dsub                         
00000a49  __subdf3                             
00000a53  __adddf3                             
00000a53  __aeabi_dadd                         
00000bdb  ADC0_IRQHandler                      
00000bdb  ADC1_IRQHandler                      
00000bdb  AES_IRQHandler                       
00000bdb  CANFD0_IRQHandler                    
00000bdb  DAC0_IRQHandler                      
00000bdb  DMA_IRQHandler                       
00000bdb  Default_Handler                      
00000bdb  GROUP0_IRQHandler                    
00000bdb  GROUP1_IRQHandler                    
00000bdb  HardFault_Handler                    
00000bdb  I2C0_IRQHandler                      
00000bdb  I2C1_IRQHandler                      
00000bdb  NMI_Handler                          
00000bdb  PendSV_Handler                       
00000bdb  RTC_IRQHandler                       
00000bdb  SPI0_IRQHandler                      
00000bdb  SPI1_IRQHandler                      
00000bdb  SVC_Handler                          
00000bdb  SysTick_Handler                      
00000bdb  TIMA0_IRQHandler                     
00000bdb  TIMA1_IRQHandler                     
00000bdb  TIMG0_IRQHandler                     
00000bdb  TIMG12_IRQHandler                    
00000bdb  TIMG6_IRQHandler                     
00000bdb  TIMG7_IRQHandler                     
00000bdb  TIMG8_IRQHandler                     
00000bdb  UART0_IRQHandler                     
00000bdb  UART1_IRQHandler                     
00000bdb  UART2_IRQHandler                     
00000bdb  UART3_IRQHandler                     
00000bdd  Get_Analog_value                     
00000d4d  No_Mcu_Ganv_Sensor_Task_Without_tick 
00000e6d  __aeabi_ddiv                         
00000e6d  __divdf3                             
00000f79  DL_Timer_initTimerMode               
00001000  __SYSMEM_SIZE                        
00001061  __aeabi_dmul                         
00001061  __muldf3                             
00001145  adc_getValue                         
00001225  DL_SYSCTL_configSYSPLL               
00001301  memcpy                               
0000139b  __aeabi_idiv0                        
0000139d  SYSCFG_DL_SYSCTL_init                
00001419  __TI_decompress_lzss                 
00001495  __gedf2                              
00001495  __gtdf2                              
00001509  __cmpdf2                             
00001509  __eqdf2                              
00001509  __ledf2                              
00001509  __ltdf2                              
00001509  __nedf2                              
00001571  DL_SYSCTL_setHFCLKSourceHFXTParams   
000015d5  __aeabi_dcmpeq                       
000015e9  __aeabi_dcmplt                       
000015fd  __aeabi_dcmple                       
00001611  __aeabi_dcmpge                       
00001625  __aeabi_dcmpgt                       
00001637  memset                               
00001699  SYSCFG_DL_UART_0_init                
000016f9  SYSCFG_DL_I2C_0_init                 
0000174d  SYSCFG_DL_initPower                  
000017a1  SYSCFG_DL_ADC1_init                  
000017ed  __aeabi_d2iz                         
000017ed  __fixdfsi                            
00001839  DL_UART_init                         
00001881  SYSCFG_DL_GPIO_init                  
000018c9  SYSCFG_DL_TIMER_1_init               
00001911  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001955  delay_ms                             
00001999  DL_ADC12_setClockConfig              
000019d9  __aeabi_uidiv                        
000019d9  __aeabi_uidivmod                     
00001a19  __TI_auto_init_nobinit_nopinit       
00001a55  __muldsi3                            
00001a91  sprintf                              
00001ac9  uart0_send_string                    
00001afd  __aeabi_i2d                          
00001afd  __floatsidf                          
00001b29  _c_int00_noargs                      
00001b51  DL_I2C_setClockConfig                
00001b77  SYSCFG_DL_init                       
00001b9d  __aeabi_ui2d                         
00001b9d  __floatunsidf                        
00001bc1  memccpy                              
00001be3  Get_Normalize_For_User               
00001c03  Get_Anolog_Value                     
00001c21  DL_Timer_setClockConfig              
00001c3d  SYSCFG_DL_SYSTICK_init               
00001c71  __TI_zero_init_nomemset              
00001c87  DL_UART_setClockConfig               
00001c99  __TI_decompress_none                 
00001cad  __aeabi_memset                       
00001cad  __aeabi_memset4                      
00001cad  __aeabi_memset8                      
00001cc9  __aeabi_memclr                       
00001cc9  __aeabi_memclr4                      
00001cc9  __aeabi_memclr8                      
00001cd5  DL_Common_delayCycles                
00001cdf  No_MCU_Ganv_Sensor_Init_Frist        
00001cf5  __aeabi_memcpy                       
00001cf5  __aeabi_memcpy4                      
00001cf5  __aeabi_memcpy8                      
00001cfd  Get_Digtal_For_User                  
00001d03  abort                                
00001d08  C$$EXIT                              
00001d09  HOSTexit                             
00001d0d  Reset_Handler                        
00001d11  _system_pre_init                     
00001e3c  __TI_Handler_Table_Base              
00001e48  __TI_Handler_Table_Limit             
00001e50  __TI_CINIT_Base                      
00001e60  __TI_CINIT_Limit                     
00001e60  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  _sys_memory                          
20201000  rx_buff                              
20201100  Anolog                               
20201110  black                                
20201120  white                                
20201130  Normal                               
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[152 symbols]
